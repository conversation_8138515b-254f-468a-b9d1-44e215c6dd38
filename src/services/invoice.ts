import { supabase } from "@/integrations/supabase/client";
import type { Customer } from "./customer";

export interface InvoiceItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  price: number;
  amount: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  date: string;
  customer_id: string;
  customer?: Customer;
  items?: InvoiceItem[];
  subtotal: number;
  discount: number;
  discount_percentage: number;
  total: number;
  status: "paid" | "unpaid" | "pending";
  notes?: string;
  order_received_date?: string;
  delivery_date?: string;
  type?: string;
}

export const invoiceService = {
  async getAll(): Promise<Invoice[]> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching invoices:", error);
      throw error;
    }

    return (data || []).map((invoice) => ({
      ...invoice,
      status: invoice.status as "paid" | "unpaid" | "pending",
    }));
  },

  async getById(id: string): Promise<Invoice | null> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching invoice:", error);
      return null;
    }

    return data
      ? {
          ...data,
          status: data.status as "paid" | "unpaid" | "pending",
        }
      : null;
  },

  async create(
    invoice: Omit<Invoice, "id" | "invoice_number">
  ): Promise<Invoice> {
    // Generate invoice number
    const { data: invoiceNumberData } = await supabase.rpc(
      "generate_invoice_number"
    );

    const invoiceNumber = invoiceNumberData || `INV${Date.now()}`;

    const { data, error } = await supabase
      .from("invoices")
      .insert([
        {
          ...invoice,
          invoice_number: invoiceNumber,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating invoice:", error);
      throw error;
    }

    // Create invoice items if provided
    if (invoice.items && invoice.items.length > 0) {
      const itemsToInsert = invoice.items.map((item) => ({
        invoice_id: data.id,
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        price: item.price,
        amount: item.amount,
      }));

      const { error: itemsError } = await supabase
        .from("invoice_items")
        .insert(itemsToInsert);

      if (itemsError) {
        console.error("Error creating invoice items:", itemsError);
        throw itemsError;
      }
    }

    return {
      ...data,
      status: data.status as "paid" | "unpaid" | "pending",
    };
  },

  async update(id: string, invoice: Partial<Invoice>): Promise<Invoice> {
    const { data, error } = await supabase
      .from("invoices")
      .update(invoice)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating invoice:", error);
      throw error;
    }

    return {
      ...data,
      status: data.status as "paid" | "unpaid" | "pending",
    };
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("invoices").delete().eq("id", id);

    if (error) {
      console.error("Error deleting invoice:", error);
      throw error;
    }
  },
};
